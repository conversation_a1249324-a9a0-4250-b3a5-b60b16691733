import React from 'react';
import { 
  <PERSON>, 
  Text, 
  XStack, 
  YS<PERSON>ck, 
  Button,
} from 'tamagui';
import { FileText, FileImage, Trash2, MoveVertical as MoreVertical, BookOpen } from 'lucide-react-native';
import { Document } from '@/state/contentStore';

interface DocumentCardProps {
  document: Document;
  onOpen: (document: Document) => void;
  onDelete: (documentId: string) => void;
}

export function DocumentCard({ document, onOpen, onDelete }: DocumentCardProps) {
  const isImage = document.file_type === 'image';
  
  return (
    <Card
      bordered
      elevate
      size="$4"
      mb="$2"
      animation="bouncy"
      scale={0.97}
      pressStyle={{ scale: 1 }}
      onPress={() => onOpen(document)}
    >
      <Card.Header padded>
        <XStack alignItems="center" space="$2">
          {isImage ? (
            <FileImage size={20} color="#9966FF" />
          ) : (
            <FileText size={20} color="#3694FF" />
          )}
          <Text fontSize="$5" fontWeight="bold" numberOfLines={1}>
            {document.title}
          </Text>
        </XStack>
      </Card.Header>
      <Card.Footer padded>
        <XStack justifyContent="space-between" alignItems="center" f={1}>
          <Text theme="alt2" fontSize="$3">
            {new Date(document.created_at).toLocaleDateString()}
          </Text>
          <XStack space="$2">
            <Button 
              size="$2" 
              onPress={() => onOpen(document)}
              icon={<BookOpen size={16} />}
              themeInverse
            >
              Open
            </Button>
            <Button 
              size="$2" 
              onPress={() => onDelete(document.id)}
              icon={<Trash2 size={16} />}
              bg="$red5"
              color="$red10"
            />
          </XStack>
        </XStack>
      </Card.Footer>
    </Card>
  );
}