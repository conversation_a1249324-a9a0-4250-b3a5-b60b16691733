import * as SecureStore from 'expo-secure-store';
import { createClient } from '@supabase/supabase-js';
import { Platform } from 'react-native';

// Use environment variables in production
const supabaseUrl = 'https://your-supabase-url.supabase.co';
const supabaseAnonKey = 'your-anon-key';

// Custom storage adapter for SecureStore
const ExpoSecureStoreAdapter = {
  getItem: (key: string) => {
    return SecureStore.getItemAsync(key);
  },
  setItem: (key: string, value: string) => {
    SecureStore.setItemAsync(key, value);
  },
  removeItem: (key: string) => {
    SecureStore.deleteItemAsync(key);
  },
};

// For web, use localStorage instead of SecureStore
const storageAdapter = Platform.OS === 'web' 
  ? undefined 
  : ExpoSecureStoreAdapter;

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: storageAdapter,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
  },
});

// Export session helper
export const getSession = async () => {
  try {
    const { data, error } = await supabase.auth.getSession();
    if (error) console.error('Error getting session:', error.message);
    return data?.session;
  } catch (error) {
    console.error('Error in getSession:', error);
    return null;
  }
};