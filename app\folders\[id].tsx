import React, { useEffect, useState } from 'react';
import { Platform, StatusBar, RefreshControl } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { 
  YStack, 
  XStack, 
  Text, 
  Button, 
  ScrollView, 
  H3,
  Spinner,
  Theme,
  View,
} from 'tamagui';
import { 
  ChevronLeft,
  Plus,
  FileText,
} from 'lucide-react-native';
import { useContentStore, Document } from '@/state/contentStore';
import { DocumentCard } from '@/components/DocumentCard';
import { UploadButton } from '@/components/UploadButton';

export default function FolderDetailScreen() {
  const insets = useSafeAreaInsets();
  const router = useRouter();
  const { id } = useLocalSearchParams();
  const folderId = Array.isArray(id) ? id[0] : id;
  
  const [refreshing, setRefreshing] = useState(false);
  
  const { 
    folders, 
    documents, 
    fetchFolders, 
    fetchDocuments, 
    isLoading 
  } = useContentStore();
  
  // Find the current folder
  const currentFolder = folders.find(folder => folder.id === folderId);
  
  useEffect(() => {
    if (folders.length === 0) {
      fetchFolders();
    }
    
    if (folderId) {
      fetchDocuments(folderId);
    }
  }, [folderId, fetchFolders, fetchDocuments, folders.length]);
  
  const onRefresh = async () => {
    setRefreshing(true);
    if (folderId) {
      await fetchDocuments(folderId);
    }
    setRefreshing(false);
  };
  
  const handleOpenDocument = (document: Document) => {
    router.push({
      pathname: '/document/[id]',
      params: { id: document.id }
    });
  };
  
  const handleDeleteDocument = async (documentId: string) => {
    // Implement delete document functionality
    console.log('Delete document:', documentId);
  };
  
  return (
    <Theme name="light">
      <YStack f={1} bg="$background">
        <YStack pt={Platform.OS === 'ios' ? insets.top : insets.top + StatusBar.currentHeight} px="$4" pb="$8" f={1}>
          {/* Header section */}
          <XStack alignItems="center" space="$2" mb="$4">
            <Button
              size="$3"
              circular
              icon={<ChevronLeft size={18} />}
              onPress={() => router.back()}
              bg="$gray5"
            />
            <H3>{currentFolder?.name || 'Folder'}</H3>
          </XStack>
          
          {/* Actions */}
          <XStack justifyContent="space-between" alignItems="center" mb="$4">
            <Text theme="alt2">
              {documents.length} {documents.length === 1 ? 'document' : 'documents'}
            </Text>
            <UploadButton 
              folderId={folderId} 
              onUploadComplete={onRefresh} 
            />
          </XStack>
          
          {/* Document list */}
          {isLoading ? (
            <YStack f={1} justifyContent="center" alignItems="center">
              <Spinner size="large" />
            </YStack>
          ) : (
            <ScrollView
              refreshControl={
                <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
              }
              contentContainerStyle={{ flexGrow: 1 }}
            >
              <YStack space="$2">
                {documents.length > 0 ? (
                  documents.map((document) => (
                    <DocumentCard
                      key={document.id}
                      document={document}
                      onOpen={handleOpenDocument}
                      onDelete={handleDeleteDocument}
                    />
                  ))
                ) : (
                  <YStack space="$4" alignItems="center" py="$10">
                    <FileText size={48} color="#94A3B8" />
                    <Text theme="alt2">No documents in this folder</Text>
                    <Button 
                      onPress={() => {
                        // Open document picker
                      }}
                      size="$3"
                      icon={<Plus size={16} />}
                      themeInverse
                    >
                      Add Document
                    </Button>
                  </YStack>
                )}
              </YStack>
            </ScrollView>
          )}
        </YStack>
      </YStack>
    </Theme>
  );
}