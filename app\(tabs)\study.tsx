import React, { useState } from 'react';
import { Platform, StatusBar } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { 
  YStack, 
  XStack, 
  Text, 
  Button, 
  ScrollView, 
  H2, 
  Card, 
  Input, 
  TextArea,
  Spinner,
  Theme,
} from 'tamagui';
import { BookText, Slash as FlashCard, FileQuestion, Send, Brain, Sparkles } from 'lucide-react-native';
import { useAuthStore } from '@/state/authStore';
import { generateNotes, generateFlashcards, generateQuiz } from '@/lib/ai';

export default function StudyScreen() {
  const insets = useSafeAreaInsets();
  const router = useRouter();
  const { user } = useAuthStore();
  
  const [topic, setTopic] = useState('');
  const [processing, setProcessing] = useState(false);
  const [result, setResult] = useState<{
    type: 'notes' | 'flashcards' | 'quiz';
    data: any;
  } | null>(null);
  
  const handleGenerateNotes = async () => {
    if (!topic.trim()) return;
    
    try {
      setProcessing(true);
      const notes = await generateNotes(topic);
      setResult({
        type: 'notes',
        data: notes,
      });
    } catch (error) {
      console.error('Error generating notes:', error);
    } finally {
      setProcessing(false);
    }
  };
  
  const handleGenerateFlashcards = async () => {
    if (!topic.trim()) return;
    
    try {
      setProcessing(true);
      const flashcards = await generateFlashcards(topic);
      setResult({
        type: 'flashcards',
        data: flashcards,
      });
    } catch (error) {
      console.error('Error generating flashcards:', error);
    } finally {
      setProcessing(false);
    }
  };
  
  const handleGenerateQuiz = async () => {
    if (!topic.trim()) return;
    
    try {
      setProcessing(true);
      const quiz = await generateQuiz(topic);
      setResult({
        type: 'quiz',
        data: quiz,
      });
    } catch (error) {
      console.error('Error generating quiz:', error);
    } finally {
      setProcessing(false);
    }
  };
  
  return (
    <Theme name="light">
      <YStack f={1} bg="$background">
        <ScrollView>
          <YStack pt={Platform.OS === 'ios' ? insets.top : insets.top + StatusBar.currentHeight} px="$4" pb="$8">
            {/* Header section */}
            <H2 mb="$4">Generate Study Material</H2>
            
            {/* Input section */}
            <Card bordered elevate size="$4" mb="$4">
              <Card.Header padded>
                <XStack alignItems="center" space="$2">
                  <Brain size={20} color="#3694FF" />
                  <Text fontSize="$5" fontWeight="bold">Enter a topic to study</Text>
                </XStack>
              </Card.Header>
              <Card.Footer padded>
                <YStack space="$3" width="100%">
                  <TextArea
                    placeholder="e.g., Quantum Physics, World War II, Photosynthesis..."
                    value={topic}
                    onChangeText={setTopic}
                    size="$4"
                    numberOfLines={4}
                    autoCapitalize="none"
                    borderWidth={1}
                    borderColor="$borderColor"
                  />
                  <XStack flexWrap="wrap" justifyContent="space-between" space="$2">
                    <Button 
                      onPress={handleGenerateNotes}
                      disabled={!topic.trim() || processing} 
                      size="$3" 
                      icon={<BookText size={16} />}
                      bg="$blue10"
                      color="white"
                      pressStyle={{ opacity: 0.8 }}
                      mb="$2"
                      minWidth={100}
                      flex={1}
                    >
                      Notes
                    </Button>
                    <Button 
                      onPress={handleGenerateFlashcards}
                      disabled={!topic.trim() || processing} 
                      size="$3" 
                      icon={<FlashCard size={16} />}
                      bg="$green10"
                      color="white"
                      pressStyle={{ opacity: 0.8 }}
                      mb="$2"
                      minWidth={100}
                      flex={1}
                    >
                      Flashcards
                    </Button>
                    <Button 
                      onPress={handleGenerateQuiz}
                      disabled={!topic.trim() || processing} 
                      size="$3" 
                      icon={<FileQuestion size={16} />}
                      bg="$purple10"
                      color="white"
                      pressStyle={{ opacity: 0.8 }}
                      mb="$2"
                      minWidth={100}
                      flex={1}
                    >
                      Quiz
                    </Button>
                  </XStack>
                </YStack>
              </Card.Footer>
            </Card>
            
            {/* Results Section */}
            {processing && (
              <YStack alignItems="center" py="$8" space="$4">
                <Spinner size="large" color="$blue10" />
                <Text>Generating content with AI...</Text>
              </YStack>
            )}
            
            {result && !processing && (
              <Card bordered elevate size="$4">
                <Card.Header padded>
                  <XStack alignItems="center" space="$2">
                    <Sparkles size={20} color="#9966FF" />
                    <Text fontSize="$5" fontWeight="bold">
                      Generated {result.type.charAt(0).toUpperCase() + result.type.slice(1)}
                    </Text>
                  </XStack>
                </Card.Header>
                <Card.Footer padded>
                  {result.type === 'notes' && (
                    <YStack space="$2">
                      <TextArea
                        value={result.data}
                        readOnly
                        multiline
                        numberOfLines={10}
                        size="$3"
                      />
                      <Button
                        onPress={() => {
                          // Save to folder functionality
                          router.push('/folders');
                        }}
                        themeInverse
                      >
                        Save to Folder
                      </Button>
                    </YStack>
                  )}
                  
                  {result.type === 'flashcards' && (
                    <YStack space="$3">
                      <Text>Generated {result.data.length} flashcards</Text>
                      <ScrollView horizontal>
                        <XStack space="$2" paddingRight="$4">
                          {result.data.slice(0, 3).map((card: any, index: number) => (
                            <Card
                              key={index}
                              size="$4"
                              bordered
                              width={250}
                              height={180}
                              scale={0.95}
                              pressStyle={{ scale: 1 }}
                            >
                              <YStack f={1} justifyContent="center" alignItems="center" padding="$3">
                                <Text textAlign="center" fontWeight="bold">{card.question}</Text>
                                <Text theme="alt2" marginTop="$2" textAlign="center">{card.answer}</Text>
                              </YStack>
                            </Card>
                          ))}
                        </XStack>
                      </ScrollView>
                      <Text theme="alt2">Swipe to see more</Text>
                      <Button
                        onPress={() => {
                          router.push('/flashcards');
                        }}
                        themeInverse
                      >
                        Save Flashcards
                      </Button>
                    </YStack>
                  )}
                  
                  {result.type === 'quiz' && (
                    <YStack space="$3">
                      <Text>Generated {result.data.length} quiz questions</Text>
                      <Card bordered size="$4" marginVertical="$2">
                        <Card.Header padded>
                          <Text fontWeight="bold">{result.data[0].question}</Text>
                        </Card.Header>
                        <Card.Footer padded>
                          <YStack space="$2">
                            {result.data[0].options.map((option: string, idx: number) => (
                              <XStack key={idx} space="$2">
                                <Text>{String.fromCharCode(65 + idx)}.</Text>
                                <Text>{option}</Text>
                              </XStack>
                            ))}
                            <Text marginTop="$2">
                              Answer: {result.data[0].correctAnswer}
                            </Text>
                          </YStack>
                        </Card.Footer>
                      </Card>
                      <Button
                        onPress={() => {
                          router.push('/folders');
                        }}
                        themeInverse
                      >
                        Save Quiz
                      </Button>
                    </YStack>
                  )}
                </Card.Footer>
              </Card>
            )}
            
            {/* Suggestions */}
            <YStack space="$2" mt="$6">
              <Text fontSize="$5" fontWeight="bold">Suggested Topics</Text>
              <XStack flexWrap="wrap" justifyContent="flex-start" gap="$2">
                <Button 
                  variant="outlined" 
                  size="$3" 
                  onPress={() => setTopic('Quantum Computing')}
                >
                  Quantum Computing
                </Button>
                <Button 
                  variant="outlined" 
                  size="$3" 
                  onPress={() => setTopic('Climate Change')}
                >
                  Climate Change
                </Button>
                <Button 
                  variant="outlined" 
                  size="$3" 
                  onPress={() => setTopic('Machine Learning Basics')}
                >
                  ML Basics
                </Button>
                <Button 
                  variant="outlined" 
                  size="$3" 
                  onPress={() => setTopic('World War II')}
                >
                  World War II
                </Button>
              </XStack>
            </YStack>
          </YStack>
        </ScrollView>
      </YStack>
    </Theme>
  );
}