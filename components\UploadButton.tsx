import React, { useState } from 'react';
import { Platform } from 'react-native';
import * as DocumentPicker from 'expo-document-picker';
import { 
  YStack,
  XStack,
  Button, 
  Text,
  Sheet,
  Input,
} from 'tamagui';
import { 
  Upload, 
  FileText, 
  FileImage, 
  X
} from 'lucide-react-native';
import { useContentStore } from '@/state/contentStore';
import { supabase } from '@/lib/supabase';

interface UploadButtonProps {
  folderId: string;
  onUploadComplete: () => void;
}

export function UploadButton({ folderId, onUploadComplete }: UploadButtonProps) {
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const [position, setPosition] = useState(0);
  const [fileName, setFileName] = useState('');
  const [selectedFile, setSelectedFile] = useState<{
    uri: string;
    name: string;
    type: string;
  } | null>(null);
  const [uploading, setUploading] = useState(false);
  
  const { createDocument } = useContentStore();
  
  const pickDocument = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: ['application/pdf', 'image/*'],
        copyToCacheDirectory: true,
      });
      
      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        setSelectedFile({
          uri: asset.uri,
          name: asset.name,
          type: asset.mimeType || '',
        });
        
        // Set suggested file name (without extension)
        const nameWithoutExt = asset.name.replace(/\.[^/.]+$/, '');
        setFileName(nameWithoutExt);
      }
    } catch (error) {
      console.error('Error picking document:', error);
    }
  };
  
  const uploadFile = async () => {
    if (!selectedFile || !fileName.trim()) return;
    
    try {
      setUploading(true);
      
      // Determine file type
      const isPdf = selectedFile.type === 'application/pdf';
      const fileType = isPdf ? 'pdf' : 'image';
      
      // Create file name for storage
      const fileExt = selectedFile.name.split('.').pop();
      const storagePath = `${folderId}/${Date.now()}.${fileExt}`;
      
      // Upload to Supabase storage
      const formData = new FormData();
      formData.append('file', {
        uri: selectedFile.uri,
        name: `${fileName}.${fileExt}`,
        type: selectedFile.type,
      } as any);
      
      // For web, we need a different approach since FormData works differently
      let fileUrl;
      if (Platform.OS === 'web') {
        const response = await fetch(selectedFile.uri);
        const blob = await response.blob();
        const { data, error } = await supabase.storage
          .from('documents')
          .upload(storagePath, blob);
          
        if (error) throw error;
        
        const { data: publicUrlData } = supabase.storage
          .from('documents')
          .getPublicUrl(storagePath);
        
        fileUrl = publicUrlData.publicUrl;
      } else {
        const { data, error } = await supabase.storage
          .from('documents')
          .upload(storagePath, formData);
          
        if (error) throw error;
        
        const { data: publicUrlData } = supabase.storage
          .from('documents')
          .getPublicUrl(storagePath);
        
        fileUrl = publicUrlData.publicUrl;
      }
      
      // Create document in database
      await createDocument(folderId, fileName, fileUrl, fileType);
      
      // Reset form and close sheet
      setFileName('');
      setSelectedFile(null);
      setPosition(0);
      setIsSheetOpen(false);
      
      // Call the completion callback
      onUploadComplete();
      
    } catch (error) {
      console.error('Error uploading file:', error);
    } finally {
      setUploading(false);
    }
  };
  
  return (
    <>
      <Button
        size="$3"
        icon={<Upload size={18} />}
        themeInverse
        onPress={() => {
          setIsSheetOpen(true);
          setPosition(1);
        }}
      >
        Upload
      </Button>
      
      <Sheet
        modal
        open={isSheetOpen}
        onOpenChange={(open) => {
          setIsSheetOpen(open);
          if (!open) {
            setPosition(0);
            setFileName('');
            setSelectedFile(null);
          }
        }}
        snapPoints={[50]}
        position={position}
        onPositionChange={setPosition}
        dismissOnSnapToBottom
      >
        <Sheet.Overlay />
        <Sheet.Frame padding="$4">
          <Sheet.Handle />
          
          <YStack space="$4">
            <XStack justifyContent="space-between" alignItems="center">
              <Text fontSize="$5" fontWeight="bold">Upload Document</Text>
              <Button
                circular
                icon={<X size={16} />}
                onPress={() => {
                  setIsSheetOpen(false);
                  setPosition(0);
                }}
                size="$3"
                bg="$gray5"
              />
            </XStack>
            
            <Button
              onPress={pickDocument}
              size="$4"
              icon={selectedFile ? (
                selectedFile.type === 'application/pdf' ? (
                  <FileText size={18} />
                ) : (
                  <FileImage size={18} />
                )
              ) : (
                <Upload size={18} />
              )}
              marginBottom="$2"
            >
              {selectedFile ? 'Change File' : 'Select File'}
            </Button>
            
            {selectedFile && (
              <YStack space="$3">
                <Text numberOfLines={1} ellipsizeMode="middle">
                  Selected: {selectedFile.name}
                </Text>
                
                <Input
                  placeholder="Document Title"
                  value={fileName}
                  onChangeText={setFileName}
                  size="$4"
                />
                
                <Button
                  themeInverse
                  size="$4"
                  onPress={uploadFile}
                  disabled={uploading || !fileName.trim()}
                >
                  {uploading ? 'Uploading...' : 'Upload Document'}
                </Button>
              </YStack>
            )}
          </YStack>
        </Sheet.Frame>
      </Sheet>
    </>
  );
}