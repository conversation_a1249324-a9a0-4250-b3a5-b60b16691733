import React, { useState, useRef } from 'react';
import { Platform, StatusBar, Dimensions } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withSpring, 
  interpolate, 
  runOnJS
} from 'react-native-reanimated';
import { GestureDetector, Gesture } from 'react-native-gesture-handler';
import { 
  YStack, 
  XStack, 
  Text, 
  Button, 
  H2, 
  Card, 
  Theme,
  View,
} from 'tamagui';
import { 
  ChevronLeft, 
  ChevronRight, 
  RotateCcw, 
  Bookmark, 
  BookmarkCheck
} from 'lucide-react-native';

const SCREEN_WIDTH = Dimensions.get('window').width;

// Sample flashcards data
const SAMPLE_FLASHCARDS = [
  { id: '1', question: 'What is the capital of France?', answer: 'Paris' },
  { id: '2', question: 'What is the largest planet in our solar system?', answer: 'Jupiter' },
  { id: '3', question: 'What is the chemical symbol for gold?', answer: 'Au (Aurum)' },
  { id: '4', question: 'Who painted the Mona Lisa?', answer: '<PERSON>' },
  { id: '5', question: 'What is the powerhouse of the cell?', answer: 'Mitochondria' },
];

export default function FlashcardsScreen() {
  const insets = useSafeAreaInsets();
  
  const [currentIndex, setCurrentIndex] = useState(0);
  const [flipped, setFlipped] = useState(false);
  const [savedCards, setSavedCards] = useState<string[]>([]);
  
  const rotation = useSharedValue(0);
  const translateX = useSharedValue(0);
  
  const rotateCard = () => {
    setFlipped(!flipped);
    rotation.value = withSpring(flipped ? 0 : 180, { damping: 15 });
  };
  
  const nextCard = () => {
    if (currentIndex < SAMPLE_FLASHCARDS.length - 1) {
      translateX.value = withSpring(-SCREEN_WIDTH, {
        damping: 15,
        restDisplacementThreshold: 0.1,
      }, () => {
        runOnJS(setCurrentIndex)(currentIndex + 1);
        runOnJS(setFlipped)(false);
        rotation.value = 0;
        translateX.value = 0;
      });
    }
  };
  
  const prevCard = () => {
    if (currentIndex > 0) {
      translateX.value = withSpring(SCREEN_WIDTH, {
        damping: 15,
        restDisplacementThreshold: 0.1,
      }, () => {
        runOnJS(setCurrentIndex)(currentIndex - 1);
        runOnJS(setFlipped)(false);
        rotation.value = 0;
        translateX.value = 0;
      });
    }
  };
  
  const toggleSaved = (id: string) => {
    if (savedCards.includes(id)) {
      setSavedCards(savedCards.filter((cardId) => cardId !== id));
    } else {
      setSavedCards([...savedCards, id]);
    }
  };
  
  const dragGesture = Gesture.Pan()
    .onUpdate((e) => {
      translateX.value = e.translationX;
    })
    .onEnd((e) => {
      if (e.translationX < -100 && currentIndex < SAMPLE_FLASHCARDS.length - 1) {
        runOnJS(nextCard)();
      } else if (e.translationX > 100 && currentIndex > 0) {
        runOnJS(prevCard)();
      } else {
        translateX.value = withSpring(0);
      }
    });
  
  const frontAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: translateX.value },
        { rotateY: `${interpolate(rotation.value, [0, 180], [0, 180])}deg` },
      ],
      opacity: interpolate(rotation.value, [0, 90, 180], [1, 0, 0]),
    };
  });
  
  const backAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: translateX.value },
        { rotateY: `${interpolate(rotation.value, [0, 180], [180, 360])}deg` },
      ],
      opacity: interpolate(rotation.value, [0, 90, 180], [0, 0, 1]),
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
    };
  });
  
  const card = SAMPLE_FLASHCARDS[currentIndex];
  const isCardSaved = savedCards.includes(card.id);
  
  return (
    <Theme name="light">
      <YStack f={1} bg="$background">
        <YStack pt={Platform.OS === 'ios' ? insets.top : insets.top + StatusBar.currentHeight} px="$4" pb="$8" f={1}>
          {/* Header section */}
          <XStack justifyContent="space-between" alignItems="center" mb="$6">
            <H2>Flashcards</H2>
            <Button 
              size="$3"
              circular
              bg={isCardSaved ? '$green10' : '$gray5'}
              color={isCardSaved ? 'white' : '$gray11'}
              icon={isCardSaved ? BookmarkCheck : Bookmark}
              onPress={() => toggleSaved(card.id)}
            />
          </XStack>
          
          {/* Flashcard section */}
          <YStack f={1} justifyContent="center" alignItems="center">
            <Text mb="$2">Card {currentIndex + 1} of {SAMPLE_FLASHCARDS.length}</Text>
            
            <View width={SCREEN_WIDTH - 48} height={400}>
              <GestureDetector gesture={dragGesture}>
                <Animated.View style={frontAnimatedStyle}>
                  <Card
                    bordered
                    elevate
                    size="$4"
                    animation="bouncy"
                    width="100%"
                    height="100%"
                    onPress={rotateCard}
                    bg="$blue4"
                    borderColor="$blue7"
                  >
                    <YStack f={1} justifyContent="center" alignItems="center" p="$4">
                      <Text fontSize="$6" textAlign="center" fontWeight="bold">
                        {card.question}
                      </Text>
                      <Text theme="alt2" mt="$6" fontSize="$3" textAlign="center">
                        Tap to see answer
                      </Text>
                    </YStack>
                  </Card>
                </Animated.View>
              </GestureDetector>
              
              <GestureDetector gesture={dragGesture}>
                <Animated.View style={backAnimatedStyle}>
                  <Card
                    bordered
                    elevate
                    size="$4"
                    width="100%"
                    height="100%"
                    onPress={rotateCard}
                    bg="$green4"
                    borderColor="$green7"
                  >
                    <YStack f={1} justifyContent="center" alignItems="center" p="$4">
                      <Text fontSize="$6" textAlign="center" fontWeight="bold">
                        {card.answer}
                      </Text>
                      <Text theme="alt2" mt="$6" fontSize="$3" textAlign="center">
                        Tap to see question
                      </Text>
                    </YStack>
                  </Card>
                </Animated.View>
              </GestureDetector>
            </View>
            
            <XStack space="$4" mt="$4" alignItems="center">
              <Button
                size="$4"
                circular
                bg="$gray5"
                disabled={currentIndex === 0}
                opacity={currentIndex === 0 ? 0.5 : 1}
                icon={<ChevronLeft size={24} />}
                onPress={prevCard}
              />
              
              <Button
                size="$3"
                icon={<RotateCcw size={16} />}
                onPress={rotateCard}
                variant="outlined"
              >
                Flip
              </Button>
              
              <Button
                size="$4"
                circular
                bg="$gray5"
                disabled={currentIndex === SAMPLE_FLASHCARDS.length - 1}
                opacity={currentIndex === SAMPLE_FLASHCARDS.length - 1 ? 0.5 : 1}
                icon={<ChevronRight size={24} />}
                onPress={nextCard}
              />
            </XStack>
            
            <Text fontSize="$3" color="$gray10" mt="$4">
              Swipe left/right to navigate
            </Text>
          </YStack>
        </YStack>
      </YStack>
    </Theme>
  );
}