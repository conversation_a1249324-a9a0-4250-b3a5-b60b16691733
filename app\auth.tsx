import React, { useState } from 'react';
import { StyleSheet, KeyboardAvoidingView, Platform, TouchableWithoutFeedback, Keyboard } from 'react-native';
import { YStack, XStack, Text, Button, Input, H1, H3, Paragraph, Theme } from 'tamagui';
import { Mail } from 'lucide-react-native';
import { useAuthStore } from '@/state/authStore';

export default function AuthScreen() {
  const [email, setEmail] = useState('');
  const [emailSent, setEmailSent] = useState(false);
  const { signIn, isLoading, error } = useAuthStore();

  const handleSignIn = async () => {
    if (!email.trim()) return;
    
    await signIn(email);
    setEmailSent(true);
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <KeyboardAvoidingView 
        style={styles.container} 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'} 
        keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
      >
        <Theme name="light">
          <YStack f={1} padding="$4" justifyContent="center" space="$4">
            <YStack space="$2" marginBottom="$8">
              <H1>Notelm10</H1>
              <H3>Your AI-powered study companion</H3>
              <Paragraph theme="alt1">
                Create notes, flashcards, and quizzes with the power of AI.
              </Paragraph>
            </YStack>

            {!emailSent ? (
              <>
                <Input
                  placeholder="Email"
                  value={email}
                  onChangeText={setEmail}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoComplete="email"
                  borderColor="$borderColor"
                  padding="$3"
                  marginBottom="$2"
                  size="$4"
                />

                {error && (
                  <Text color="$red10\" marginBottom="$2">{error}</Text>
                )}

                <Button 
                  themeInverse
                  size="$4"
                  onPress={handleSignIn}
                  disabled={isLoading || !email.trim()}
                  icon={Mail}
                >
                  {isLoading ? 'Sending...' : 'Continue with Email'}
                </Button>
              </>
            ) : (
              <YStack space="$4" alignItems="center">
                <Text textAlign="center" fontSize="$6">
                  Check your inbox! We've sent a magic link to {email}
                </Text>
                <Button 
                  variant="outlined" 
                  onPress={() => setEmailSent(false)}
                >
                  Try a different email
                </Button>
              </YStack>
            )}
          </YStack>
        </Theme>
      </KeyboardAvoidingView>
    </TouchableWithoutFeedback>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
});