import React, { useState, useRef } from 'react';
import { Platform, StatusBar, KeyboardAvoidingView, FlatList } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  XStack, 
  Text, 
  Button, 
  H3,
  Input,
  Card,
  Avatar,
  Spinner,
  Theme,
} from 'tamagui';
import { 
  ChevronLeft, 
  Send, 
  <PERSON>rkles,
} from 'lucide-react-native';
import { chatWithDocument } from '@/lib/ai';

// Sample document text
const SAMPLE_DOCUMENT_TEXT = `
Quantum computing is a type of computation that harnesses the collective properties of quantum states, such as superposition, interference, and entanglement, to perform calculations. The devices that perform quantum computations are known as quantum computers. Though current quantum computers are too small to outperform usual (classical) computers for practical applications, they are believed to be capable of solving certain computational problems, such as integer factorization (which underlies RSA encryption), substantially faster than classical computers. The study of quantum computing is a subfield of quantum information science.

Quantum computers are believed to be able to solve certain computational problems, such as integer factorization (which underlies RSA encryption), substantially faster than classical computers. The study of quantum computing is a subfield of quantum information science.

Quantum computing began in the early 1980s, when physicist <PERSON> proposed a quantum mechanical model of the <PERSON>g machine. <PERSON>ynman and <PERSON> Manin later suggested that a quantum computer had the potential to simulate things that a classical computer could not. In 1994, Peter Shor developed a quantum algorithm for factoring integers that had the potential to decrypt RSA-encrypted communications. Despite ongoing experimental progress since the late 1990s, most researchers believe that "fault-tolerant quantum computing [is] still a rather distant dream." In recent years, investment in quantum computing research has increased in the public and private sectors.
`;

interface Message {
  id: string;
  text: string;
  sender: 'user' | 'ai';
  timestamp: number;
}

export default function ChatScreen() {
  const insets = useSafeAreaInsets();
  const router = useRouter();
  
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: "Hello! I'm your AI tutor. Ask me anything about the document, and I'll help you understand it better.",
      sender: 'ai',
      timestamp: Date.now(),
    },
  ]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  
  const flatListRef = useRef<FlatList>(null);
  
  const handleSendMessage = async () => {
    if (!inputText.trim()) return;
    
    const userMessage: Message = {
      id: Date.now().toString(),
      text: inputText.trim(),
      sender: 'user',
      timestamp: Date.now(),
    };
    
    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsLoading(true);
    
    try {
      // Get AI response
      const response = await chatWithDocument(
        SAMPLE_DOCUMENT_TEXT,
        userMessage.text
      );
      
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: response,
        sender: 'ai',
        timestamp: Date.now(),
      };
      
      setMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      console.error('Error getting AI response:', error);
      
      // Add error message
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: 'Sorry, I encountered an error. Please try again later.',
        sender: 'ai',
        timestamp: Date.now(),
      };
      
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };
  
  const renderMessageItem = ({ item }: { item: Message }) => (
    <XStack 
      justifyContent={item.sender === 'user' ? 'flex-end' : 'flex-start'}
      marginVertical="$2"
      paddingHorizontal="$2"
    >
      {item.sender === 'ai' && (
        <Avatar circular size="$3\" bg="$blue10\" marginRight="$2">
          <Sparkles size={16} color="white" />
        </Avatar>
      )}
      
      <Card
        bordered
        elevate
        size="$2"
        backgroundColor={item.sender === 'user' ? '$blue10' : '$gray1'}
        borderColor={item.sender === 'user' ? '$blue8' : '$gray5'}
        padding="$3"
        maxWidth="80%"
      >
        <Text 
          color={item.sender === 'user' ? 'white' : '$color'}
        >
          {item.text}
        </Text>
      </Card>
    </XStack>
  );
  
  return (
    <Theme name="light">
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
      >
        <YStack 
          f={1} 
          bg="$background" 
          pt={Platform.OS === 'ios' ? insets.top : insets.top + StatusBar.currentHeight}
          pb={Platform.OS === 'ios' ? insets.bottom : 0}
        >
          {/* Header */}
          <XStack 
            alignItems="center" 
            space="$2" 
            paddingHorizontal="$4"
            paddingVertical="$3"
            backgroundColor="$background"
            borderBottomWidth={1}
            borderBottomColor="$gray5"
          >
            <Button
              size="$3"
              circular
              icon={<ChevronLeft size={18} />}
              onPress={() => router.back()}
              bg="$gray5"
            />
            <H3>AI Tutor Chat</H3>
          </XStack>
          
          {/* Messages */}
          <FlatList
            ref={flatListRef}
            data={messages}
            renderItem={renderMessageItem}
            keyExtractor={(item) => item.id}
            contentContainerStyle={{ paddingVertical: 16 }}
            style={{ flex: 1 }}
            onContentSizeChange={() => {
              // Scroll to the bottom when content size changes
              flatListRef.current?.scrollToEnd({ animated: true });
            }}
          />
          
          {/* Input area */}
          <XStack
            alignItems="center"
            padding="$3"
            space="$2"
            backgroundColor="$background"
            borderTopWidth={1}
            borderTopColor="$gray5"
          >
            <Input
              flex={1}
              placeholder="Ask about the document..."
              value={inputText}
              onChangeText={setInputText}
              size="$3"
              borderWidth={1}
              borderColor="$borderColor"
            />
            
            {isLoading ? (
              <Button
                circular
                size="$4"
                backgroundColor="$gray5"
                disabled
              >
                <Spinner size="small\" color="$blue10" />
              </Button>
            ) : (
              <Button
                circular
                size="$4"
                themeInverse
                onPress={handleSendMessage}
                disabled={!inputText.trim()}
                icon={<Send size={20} />}
              />
            )}
          </XStack>
        </YStack>
      </KeyboardAvoidingView>
    </Theme>
  );
}