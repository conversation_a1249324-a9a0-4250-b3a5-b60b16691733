import { create } from 'zustand';
import { supabase } from '@/lib/supabase';

interface User {
  id: string;
  email: string;
}

interface AuthState {
  user: User | null;
  isLoading: boolean;
  error: string | null;
  signIn: (email: string) => Promise<void>;
  signOut: () => Promise<void>;
  checkSession: () => Promise<void>;
}

export const useAuthStore = create<AuthState>((set) => ({
  user: null,
  isLoading: false,
  error: null,
  
  signIn: async (email: string) => {
    try {
      set({ isLoading: true, error: null });
      
      const { error } = await supabase.auth.signInWithOtp({
        email,
        options: {
          emailRedirectTo: 'notelm10://auth/callback',
        }
      });
      
      if (error) throw error;
      
      set({ isLoading: false });
    } catch (error: any) {
      set({ 
        isLoading: false, 
        error: error.message || 'Failed to sign in' 
      });
    }
  },
  
  signOut: async () => {
    try {
      set({ isLoading: true, error: null });
      
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      
      set({ user: null, isLoading: false });
    } catch (error: any) {
      set({ 
        isLoading: false, 
        error: error.message || 'Failed to sign out' 
      });
    }
  },
  
  checkSession: async () => {
    try {
      set({ isLoading: true, error: null });
      
      // Mock user for testing to bypass authentication
      // Remove this for production and uncomment the actual auth code
      set({ 
        user: {
          id: 'test-user-id',
          email: '<EMAIL>',
        },
        isLoading: false 
      });
      
      /*
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error) throw error;
      
      if (session?.user) {
        set({ 
          user: {
            id: session.user.id,
            email: session.user.email || '',
          },
          isLoading: false 
        });
      } else {
        set({ user: null, isLoading: false });
      }
      */
    } catch (error: any) {
      set({ 
        user: null, 
        isLoading: false, 
        error: error.message || 'Failed to get session' 
      });
    }
  },
}));