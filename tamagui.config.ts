import { createTamagui } from 'tamagui';
import { shorthands } from '@tamagui/shorthands';
import { themes, tokens } from '@tamagui/theme-base';

const headingFont = {
  family: {
    normal: 'InterBold',
  },
  size: {
    6: 15,
    7: 18,
    8: 22,
    9: 28,
    10: 36,
    12: 48,
    14: 56,
    16: 68,
  },
  weight: {
    6: '400',
    7: '700',
  },
};

const bodyFont = {
  family: {
    normal: 'Inter',
    medium: 'InterMedium',
    bold: 'InterBold',
  },
  weight: {
    4: '400',
    5: '500',
    7: '700',
  },
};

const config = createTamagui({
  defaultFont: 'body',
  defaultTheme: 'light',
  fonts: {
    heading: headingFont,
    body: bodyFont,
  },
  tokens,
  themes,
  shorthands,
});

export type AppConfig = typeof config;

declare module 'tamagui' {
  interface TamaguiCustomConfig extends AppConfig {}
}

export default config;