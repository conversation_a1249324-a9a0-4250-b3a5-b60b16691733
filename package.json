{"name": "notelm10", "main": "expo-router/entry", "version": "1.0.0", "private": true, "scripts": {"dev": "cross-env EXPO_NO_TELEMETRY=1 expo start", "web": "cross-env EXPO_NO_TELEMETRY=1 expo start --web", "build:web": "expo export --platform web", "lint": "expo lint"}, "dependencies": {"@babel/preset-typescript": "^7.23.3", "@expo-google-fonts/inter": "^0.2.3", "@expo/vector-icons": "^14.1.0", "@google/generative-ai": "^0.1.3", "@lucide/lab": "^0.1.2", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@supabase/supabase-js": "^2.39.7", "@tamagui/config": "^1.92.2", "@tamagui/core": "^1.92.2", "@tamagui/lucide-icons": "^1.92.2", "@tamagui/shorthands": "^1.92.2", "@tamagui/theme-base": "^1.92.2", "expo": "^53.0.0", "expo-blur": "~14.1.3", "expo-camera": "~16.1.5", "expo-constants": "~17.1.3", "expo-document-picker": "~11.10.1", "expo-font": "~13.2.2", "expo-haptics": "~14.1.3", "expo-linear-gradient": "~14.1.3", "expo-linking": "~7.1.3", "expo-router": "~5.0.2", "expo-secure-store": "~12.8.1", "expo-splash-screen": "~0.30.6", "expo-status-bar": "~2.2.2", "expo-symbols": "~0.4.3", "expo-system-ui": "~5.0.5", "expo-web-browser": "~14.1.5", "lucide-react-native": "^0.475.0", "pdfjs-dist": "^4.0.379", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.1", "react-native-gesture-handler": "~2.24.0", "react-native-pdf": "^6.7.4", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.3.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "tamagui": "^1.92.2", "tesseract.js": "^5.0.4", "zustand": "^4.5.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@tamagui/babel-plugin": "^1.126.13", "@types/react": "~19.0.10", "cross-env": "^7.0.3", "typescript": "~5.8.3"}}