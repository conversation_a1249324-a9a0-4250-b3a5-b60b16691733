import { Platform } from 'react-native';

export async function extractTextFromPdf(pdfUrl: string): Promise<string> {
  try {
    if (Platform.OS === 'web') {
      try {
        // For web platform, try to use pdfjs-dist if available
        // Note: pdfjs-dist should be installed separately for web builds
        const pdfjs = await import('pdfjs-dist');

        // Initialize PDF.js worker for web
        if (typeof window !== 'undefined') {
          pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;
        }

        // Load the PDF document
        const loadingTask = pdfjs.getDocument(pdfUrl);
        const pdf = await loadingTask.promise;

        let fullText = '';

        // Iterate through each page
        for (let i = 1; i <= pdf.numPages; i++) {
          const page = await pdf.getPage(i);
          const textContent = await page.getTextContent();

          // Extract text from the page
          const pageText = textContent.items
            .map((item: any) => item.str)
            .join(' ');

          fullText += pageText + '\n';
        }

        return fullText;
      } catch (importError) {
        console.log('pdfjs-dist not available, returning placeholder text');
        return 'PDF text extraction requires the pdfjs-dist package to be installed for web builds. This is a placeholder text for the PDF content.';
      }
    } else {
      // For React Native platforms, return a placeholder
      // In a real app, you would use a React Native PDF text extraction library
      // or send the PDF to a backend service for text extraction
      console.log('PDF text extraction on mobile platforms requires additional setup');
      return 'PDF text extraction is not yet implemented for mobile platforms. This would typically be handled by a backend service or a React Native-specific PDF library.';
    }
  } catch (error) {
    console.error('Error extracting text from PDF:', error);
    throw new Error('Failed to extract text from PDF. Please try again later.');
  }
}