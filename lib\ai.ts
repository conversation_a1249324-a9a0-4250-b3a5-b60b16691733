import { Platform } from 'react-native';

// Mock implementation for development since we don't have the actual API key
const mockGenerateContent = async (prompt: string): Promise<string> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1500));
  
  // Return different mock responses based on prompt type
  if (prompt.includes('notes')) {
    return `# ${prompt.split(':')[1].trim()}\n\n## Introduction\nThis topic is fundamental to understanding the subject matter. Here are the key points to remember:\n\n- Important point one with detailed explanation\n- Critical concept two that builds on previous knowledge\n- Foundational principle three that experts always reference\n\n## Key Concepts\n1. First major concept with definition and examples\n2. Second critical idea that students often misunderstand\n3. Third principle with practical applications\n\n## Summary\nIn conclusion, these concepts work together to form a comprehensive understanding of the topic. Remember to connect these ideas when studying related subjects.`;
  }
  
  if (prompt.includes('flashcards')) {
    return `[
      {"question": "What is the primary function of this concept?", "answer": "It serves as the foundation for understanding more complex ideas."},
      {"question": "Who developed this theory and when?", "answer": "The theory was developed by <PERSON><PERSON> in 1982."},
      {"question": "What are the three main components?", "answer": "Analysis, synthesis, and evaluation."},
      {"question": "How does this apply in real-world scenarios?", "answer": "It's commonly used in engineering, medicine, and education."},
      {"question": "What is the relationship between this concept and related theories?", "answer": "It builds upon previous frameworks while introducing novel approaches."}
    ]`;
  }
  
  if (prompt.includes('quiz')) {
    return `[
      {
        "question": "Which of the following best describes the core principle?",
        "options": ["Option A with partial truth", "Option B with common misconception", "Option C with the correct answer", "Option D with related but incorrect idea"],
        "correctAnswer": "Option C with the correct answer",
        "explanation": "Option C is correct because it encompasses all the essential elements of the principle."
      },
      {
        "question": "What would happen if this principle were applied differently?",
        "options": ["Outcome A", "Outcome B", "Outcome C", "Outcome D"],
        "correctAnswer": "Outcome B",
        "explanation": "Outcome B would occur because of the fundamental relationship between cause and effect in this system."
      }
    ]`;
  }
  
  // Default response
  return "Generated content based on your request. This is a placeholder response for development purposes.";
};

// Placeholder for GoogleGenerativeAI if we're not in development
const createGenAI = () => {
  if (Platform.OS === 'web' && process.env.NODE_ENV === 'development') {
    return {
      getGenerativeModel: () => ({
        generateContent: async ({ contents }: any) => {
          const prompt = contents[0].parts[0].text;
          const text = await mockGenerateContent(prompt);
          return {
            response: {
              text: () => text
            }
          };
        }
      })
    };
  }
  
  // In production, we would use the actual GoogleGenerativeAI
  try {
    // This would be the actual implementation
    // const { GoogleGenerativeAI } = require('@google/generative-ai');
    // return new GoogleGenerativeAI(process.env.EXPO_PUBLIC_GEMINI_API_KEY);
    
    // For now, return our mock implementation
    return {
      getGenerativeModel: () => ({
        generateContent: async ({ contents }: any) => {
          const prompt = contents[0].parts[0].text;
          const text = await mockGenerateContent(prompt);
          return {
            response: {
              text: () => text
            }
          };
        }
      })
    };
  } catch (error) {
    console.error('Error initializing Gemini API:', error);
    
    // Fallback to mock implementation
    return {
      getGenerativeModel: () => ({
        generateContent: async ({ contents }: any) => {
          const prompt = contents[0].parts[0].text;
          const text = await mockGenerateContent(prompt);
          return {
            response: {
              text: () => text
            }
          };
        }
      })
    };
  }
};

const genAI = createGenAI();

const createChatPrompt = (context: string, question: string) => {
  return `Context: ${context}\n\nQuestion: ${question}\n\nProvide a detailed, accurate, and helpful response based only on the context provided.`;
};

export const generateNotes = async (topic: string): Promise<string> => {
  try {
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });
    
    const prompt = `Create concise, well-structured study notes on the topic: ${topic}.
    - Organize information in a clear, logical manner with headings and bullet points
    - Include key concepts, definitions, and examples
    - Format for easy readability and studying
    - Keep information accurate and educational`;
    
    const result = await model.generateContent({
      contents: [{ role: 'user', parts: [{ text: prompt }] }],
    });
    
    return result.response.text();
  } catch (error) {
    console.error('Error generating notes:', error);
    // Return fallback content for development
    return `# Study Notes: ${topic}\n\n## Key Concepts\n- Important point 1\n- Important point 2\n- Important point 3\n\n## Details\nThis is a placeholder for generated notes on ${topic}. In a production environment, this would contain AI-generated study notes.`;
  }
};

export const generateFlashcards = async (topic: string): Promise<Array<{ question: string, answer: string }>> => {
  try {
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });
    
    const prompt = `Generate 5 flashcards for studying the topic: ${topic}.
    Format each flashcard as a JSON object with "question" and "answer" fields.
    Return them as a JSON array of objects.
    Questions should test key knowledge. Answers should be concise and accurate.`;
    
    const result = await model.generateContent({
      contents: [{ role: 'user', parts: [{ text: prompt }] }],
    });
    
    const text = result.response.text();
    // Extract JSON array from response
    const jsonMatch = text.match(/\[[\s\S]*\]/);
    if (jsonMatch) {
      return JSON.parse(jsonMatch[0]);
    }
    
    // Fallback for development
    return [
      { question: `What is ${topic}?`, answer: "This is a placeholder answer for development." },
      { question: `Key concept of ${topic}?`, answer: "Another placeholder answer." },
      { question: `How does ${topic} work?`, answer: "Third placeholder answer." },
    ];
  } catch (error) {
    console.error('Error generating flashcards:', error);
    // Return fallback content for development
    return [
      { question: `What is ${topic}?`, answer: "This is a placeholder answer for development." },
      { question: `Key concept of ${topic}?`, answer: "Another placeholder answer." },
      { question: `How does ${topic} work?`, answer: "Third placeholder answer." },
    ];
  }
};

export const generateQuiz = async (topic: string): Promise<Array<{
  question: string, 
  options: string[], 
  correctAnswer: string,
  explanation: string
}>> => {
  try {
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });
    
    const prompt = `Generate 3 multiple-choice quiz questions for the topic: ${topic}.
    Format as a JSON array with objects containing:
    - "question": the question text
    - "options": array of 4 possible answers
    - "correctAnswer": the correct option from the options array
    - "explanation": brief explanation of why the answer is correct
    
    Questions should be challenging and educational, similar to standardized tests like the SAT.`;
    
    const result = await model.generateContent({
      contents: [{ role: 'user', parts: [{ text: prompt }] }],
    });
    
    const text = result.response.text();
    // Extract JSON array from response
    const jsonMatch = text.match(/\[[\s\S]*\]/);
    if (jsonMatch) {
      return JSON.parse(jsonMatch[0]);
    }
    
    // Fallback for development
    return [
      {
        question: `Question about ${topic}?`,
        options: ["Option A", "Option B", "Option C", "Option D"],
        correctAnswer: "Option C",
        explanation: "This is a placeholder explanation."
      },
      {
        question: `Another question about ${topic}?`,
        options: ["Option 1", "Option 2", "Option 3", "Option 4"],
        correctAnswer: "Option 2",
        explanation: "Another placeholder explanation."
      }
    ];
  } catch (error) {
    console.error('Error generating quiz:', error);
    // Return fallback content for development
    return [
      {
        question: `Question about ${topic}?`,
        options: ["Option A", "Option B", "Option C", "Option D"],
        correctAnswer: "Option C",
        explanation: "This is a placeholder explanation."
      },
      {
        question: `Another question about ${topic}?`,
        options: ["Option 1", "Option 2", "Option 3", "Option 4"],
        correctAnswer: "Option 2",
        explanation: "Another placeholder explanation."
      }
    ];
  }
};

export const chatWithDocument = async (documentText: string, question: string): Promise<string> => {
  try {
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });
    
    const prompt = createChatPrompt(documentText, question);
    
    const result = await model.generateContent({
      contents: [{ role: 'user', parts: [{ text: prompt }] }],
    });
    
    return result.response.text();
  } catch (error) {
    console.error('Error chatting with document:', error);
    // Return fallback content for development
    return `This is a placeholder response to your question: "${question}" based on the document. In a production environment, this would contain an AI-generated response that references the document content.`;
  }
};