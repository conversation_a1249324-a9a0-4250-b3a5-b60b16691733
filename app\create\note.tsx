import React, { useState } from 'react';
import { Platform, StatusBar } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { 
  YStack, 
  XStack, 
  Text, 
  Button, 
  H3, 
  TextA<PERSON>,
  Spinner,
  Theme,
} from 'tamagui';
import { ChevronLeft, Sparkles } from 'lucide-react-native';
import { generateNotes } from '@/lib/ai';

export default function CreateNoteScreen() {
  const insets = useSafeAreaInsets();
  const router = useRouter();
  
  const [topic, setTopic] = useState('');
  const [generatedNotes, setGeneratedNotes] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  
  const handleGenerateNotes = async () => {
    if (!topic.trim()) return;
    
    setIsLoading(true);
    
    try {
      const notes = await generateNotes(topic);
      setGeneratedNotes(notes);
    } catch (error) {
      console.error('Error generating notes:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleSaveNotes = () => {
    // Save notes to a folder
    // This would integrate with your state/database
    router.back();
  };
  
  return (
    <Theme name="light">
      <YStack f={1} bg="$background">
        <YStack pt={Platform.OS === 'ios' ? insets.top : insets.top + StatusBar.currentHeight} px="$4" pb="$8" f={1}>
          {/* Header section */}
          <XStack alignItems="center" space="$2" mb="$4">
            <Button
              size="$3"
              circular
              icon={<ChevronLeft size={18} />}
              onPress={() => router.back()}
              bg="$gray5"
            />
            <H3>Create Notes</H3>
          </XStack>
          
          {/* Topic input */}
          <YStack space="$4" mb="$4">
            <Text fontSize="$4" fontWeight="bold">Enter a topic</Text>
            <TextArea
              placeholder="e.g., Quantum Physics, World War II, Photosynthesis..."
              value={topic}
              onChangeText={setTopic}
              size="$4"
              numberOfLines={4}
              autoCapitalize="none"
            />
            
            <Button
              themeInverse
              size="$4"
              onPress={handleGenerateNotes}
              disabled={!topic.trim() || isLoading}
              icon={Sparkles}
            >
              {isLoading ? 'Generating...' : 'Generate Notes'}
            </Button>
          </YStack>
          
          {/* Loading indicator */}
          {isLoading && (
            <YStack alignItems="center" justifyContent="center" py="$8">
              <Spinner size="large" />
              <Text mt="$2">Generating your notes...</Text>
            </YStack>
          )}
          
          {/* Generated notes */}
          {generatedNotes && !isLoading && (
            <YStack space="$4" f={1}>
              <Text fontSize="$4" fontWeight="bold">Generated Notes</Text>
              <TextArea
                flex={1}
                value={generatedNotes}
                onChangeText={setGeneratedNotes}
                multiline
                numberOfLines={20}
              />
              
              <Button
                themeInverse
                size="$4"
                onPress={handleSaveNotes}
              >
                Save Notes
              </Button>
            </YStack>
          )}
        </YStack>
      </YStack>
    </Theme>
  );
}