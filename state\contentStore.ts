import { create } from 'zustand';
import { supabase } from '@/lib/supabase';

// Types
export interface Folder {
  id: string;
  name: string;
  created_at: string;
}

export interface Document {
  id: string;
  folder_id: string;
  title: string;
  file_url: string;
  file_type: 'pdf' | 'image';
  created_at: string;
}

export interface Note {
  id: string;
  folder_id: string;
  content: string;
  source: string;
  created_at: string;
}

export interface Flashcard {
  id: string;
  note_id: string;
  question: string;
  answer: string;
}

export interface Quiz {
  id: string;
  folder_id: string;
  question: string;
  options: string[];
  correct_answer: string;
  explanation: string;
  difficulty: string;
}

// Store state interface
interface ContentState {
  folders: Folder[];
  documents: Document[];
  notes: Note[];
  flashcards: Flashcard[];
  quizzes: Quiz[];
  currentFolder: Folder | null;
  isLoading: boolean;
  error: string | null;
  
  // Folder actions
  createFolder: (name: string) => Promise<Folder | null>;
  fetchFolders: () => Promise<void>;
  
  // Document actions
  createDocument: (folderID: string, title: string, fileUrl: string, fileType: 'pdf' | 'image') => Promise<Document | null>;
  fetchDocuments: (folderID: string) => Promise<void>;
  
  // Note actions
  createNote: (folderID: string, content: string, source: string) => Promise<Note | null>;
  fetchNotes: (folderID: string) => Promise<void>;
  
  // Flashcard actions
  createFlashcards: (noteID: string, flashcards: { question: string, answer: string }[]) => Promise<void>;
  fetchFlashcards: (noteID: string) => Promise<void>;
  
  // Quiz actions
  createQuiz: (folderID: string, quiz: Omit<Quiz, 'id' | 'folder_id'>) => Promise<Quiz | null>;
  fetchQuizzes: (folderID: string) => Promise<void>;
  
  // Set current folder
  setCurrentFolder: (folder: Folder | null) => void;
}

// Mock data for development
const MOCK_FOLDERS: Folder[] = [
  {
    id: '1',
    name: 'Physics Notes',
    created_at: new Date().toISOString(),
  },
  {
    id: '2',
    name: 'History',
    created_at: new Date(Date.now() - 86400000).toISOString(),
  },
  {
    id: '3',
    name: 'Computer Science',
    created_at: new Date(Date.now() - 172800000).toISOString(),
  },
];

const MOCK_DOCUMENTS: Document[] = [
  {
    id: '1',
    folder_id: '1',
    title: 'Quantum Physics',
    file_url: 'https://example.com/file.pdf',
    file_type: 'pdf',
    created_at: new Date().toISOString(),
  },
  {
    id: '2',
    folder_id: '1',
    title: 'Newtonian Mechanics',
    file_url: 'https://example.com/file2.pdf',
    file_type: 'pdf',
    created_at: new Date(Date.now() - 86400000).toISOString(),
  },
];

export const useContentStore = create<ContentState>((set, get) => ({
  folders: MOCK_FOLDERS,
  documents: [],
  notes: [],
  flashcards: [],
  quizzes: [],
  currentFolder: null,
  isLoading: false,
  error: null,
  
  // Folder actions
  createFolder: async (name: string) => {
    try {
      set({ isLoading: true, error: null });
      
      // Mock implementation for development
      const newFolder: Folder = {
        id: Date.now().toString(),
        name,
        created_at: new Date().toISOString(),
      };
      
      set((state) => ({
        folders: [newFolder, ...state.folders],
        isLoading: false,
      }));
      
      return newFolder;
      
      // Actual implementation
      /*
      const { data, error } = await supabase
        .from('folders')
        .insert({ name })
        .select()
        .single();
      
      if (error) throw error;
      
      set((state) => ({
        folders: [...state.folders, data],
        isLoading: false,
      }));
      
      return data;
      */
    } catch (error: any) {
      set({ 
        isLoading: false, 
        error: error.message || 'Failed to create folder' 
      });
      return null;
    }
  },
  
  fetchFolders: async () => {
    try {
      set({ isLoading: true, error: null });
      
      // Mock implementation for development
      set({ folders: MOCK_FOLDERS, isLoading: false });
      
      // Actual implementation
      /*
      const { data, error } = await supabase
        .from('folders')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      
      set({ folders: data, isLoading: false });
      */
    } catch (error: any) {
      set({ 
        isLoading: false, 
        error: error.message || 'Failed to fetch folders' 
      });
    }
  },
  
  // Document actions
  createDocument: async (folderID: string, title: string, fileUrl: string, fileType: 'pdf' | 'image') => {
    try {
      set({ isLoading: true, error: null });
      
      // Mock implementation for development
      const newDocument: Document = {
        id: Date.now().toString(),
        folder_id: folderID,
        title,
        file_url: fileUrl,
        file_type: fileType,
        created_at: new Date().toISOString(),
      };
      
      set((state) => ({
        documents: [newDocument, ...state.documents],
        isLoading: false,
      }));
      
      return newDocument;
      
      // Actual implementation
      /*
      const { data, error } = await supabase
        .from('documents')
        .insert({
          folder_id: folderID,
          title,
          file_url: fileUrl,
          file_type: fileType,
        })
        .select()
        .single();
      
      if (error) throw error;
      
      set((state) => ({
        documents: [...state.documents, data],
        isLoading: false,
      }));
      
      return data;
      */
    } catch (error: any) {
      set({ 
        isLoading: false, 
        error: error.message || 'Failed to create document' 
      });
      return null;
    }
  },
  
  fetchDocuments: async (folderID: string) => {
    try {
      set({ isLoading: true, error: null });
      
      // Mock implementation for development
      const filteredDocuments = MOCK_DOCUMENTS.filter(doc => doc.folder_id === folderID);
      set({ documents: filteredDocuments, isLoading: false });
      
      // Actual implementation
      /*
      const { data, error } = await supabase
        .from('documents')
        .select('*')
        .eq('folder_id', folderID)
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      
      set({ documents: data, isLoading: false });
      */
    } catch (error: any) {
      set({ 
        isLoading: false, 
        error: error.message || 'Failed to fetch documents' 
      });
    }
  },
  
  // Other actions for notes, flashcards, quizzes...
  createNote: async (folderID: string, content: string, source: string) => {
    try {
      set({ isLoading: true, error: null });
      
      // Mock implementation for development
      const newNote: Note = {
        id: Date.now().toString(),
        folder_id: folderID,
        content,
        source,
        created_at: new Date().toISOString(),
      };
      
      set((state) => ({
        notes: [newNote, ...state.notes],
        isLoading: false,
      }));
      
      return newNote;
      
      // Actual implementation
      /*
      const { data, error } = await supabase
        .from('notes')
        .insert({
          folder_id: folderID,
          content,
          source,
        })
        .select()
        .single();
      
      if (error) throw error;
      
      set((state) => ({
        notes: [...state.notes, data],
        isLoading: false,
      }));
      
      return data;
      */
    } catch (error: any) {
      set({ 
        isLoading: false, 
        error: error.message || 'Failed to create note' 
      });
      return null;
    }
  },
  
  fetchNotes: async (folderID: string) => {
    try {
      set({ isLoading: true, error: null });
      
      // Mock implementation for development
      set({ notes: [], isLoading: false });
      
      // Actual implementation
      /*
      const { data, error } = await supabase
        .from('notes')
        .select('*')
        .eq('folder_id', folderID)
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      
      set({ notes: data, isLoading: false });
      */
    } catch (error: any) {
      set({ 
        isLoading: false, 
        error: error.message || 'Failed to fetch notes' 
      });
    }
  },
  
  createFlashcards: async (noteID: string, flashcards: { question: string, answer: string }[]) => {
    try {
      set({ isLoading: true, error: null });
      
      // Mock implementation for development
      const newFlashcards: Flashcard[] = flashcards.map(card => ({
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        note_id: noteID,
        question: card.question,
        answer: card.answer,
      }));
      
      set((state) => ({
        flashcards: [...state.flashcards, ...newFlashcards],
        isLoading: false,
      }));
      
      // Actual implementation
      /*
      const flashcardsWithNoteID = flashcards.map(card => ({
        ...card,
        note_id: noteID,
      }));
      
      const { data, error } = await supabase
        .from('flashcards')
        .insert(flashcardsWithNoteID)
        .select();
      
      if (error) throw error;
      
      set((state) => ({
        flashcards: [...state.flashcards, ...data],
        isLoading: false,
      }));
      */
    } catch (error: any) {
      set({ 
        isLoading: false, 
        error: error.message || 'Failed to create flashcards' 
      });
    }
  },
  
  fetchFlashcards: async (noteID: string) => {
    try {
      set({ isLoading: true, error: null });
      
      // Mock implementation for development
      set({ flashcards: [], isLoading: false });
      
      // Actual implementation
      /*
      const { data, error } = await supabase
        .from('flashcards')
        .select('*')
        .eq('note_id', noteID);
      
      if (error) throw error;
      
      set({ flashcards: data, isLoading: false });
      */
    } catch (error: any) {
      set({ 
        isLoading: false, 
        error: error.message || 'Failed to fetch flashcards' 
      });
    }
  },
  
  createQuiz: async (folderID: string, quiz: Omit<Quiz, 'id' | 'folder_id'>) => {
    try {
      set({ isLoading: true, error: null });
      
      // Mock implementation for development
      const newQuiz: Quiz = {
        id: Date.now().toString(),
        folder_id: folderID,
        ...quiz
      };
      
      set((state) => ({
        quizzes: [...state.quizzes, newQuiz],
        isLoading: false,
      }));
      
      return newQuiz;
      
      // Actual implementation
      /*
      const { data, error } = await supabase
        .from('quizzes')
        .insert({
          folder_id: folderID,
          ...quiz,
        })
        .select()
        .single();
      
      if (error) throw error;
      
      set((state) => ({
        quizzes: [...state.quizzes, data],
        isLoading: false,
      }));
      
      return data;
      */
    } catch (error: any) {
      set({ 
        isLoading: false, 
        error: error.message || 'Failed to create quiz' 
      });
      return null;
    }
  },
  
  fetchQuizzes: async (folderID: string) => {
    try {
      set({ isLoading: true, error: null });
      
      // Mock implementation for development
      set({ quizzes: [], isLoading: false });
      
      // Actual implementation
      /*
      const { data, error } = await supabase
        .from('quizzes')
        .select('*')
        .eq('folder_id', folderID);
      
      if (error) throw error;
      
      set({ quizzes: data, isLoading: false });
      */
    } catch (error: any) {
      set({ 
        isLoading: false, 
        error: error.message || 'Failed to fetch quizzes' 
      });
    }
  },
  
  setCurrentFolder: (folder) => set({ currentFolder: folder }),
}));