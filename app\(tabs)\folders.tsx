import React, { useEffect, useState } from 'react';
import { Platform, StatusBar, RefreshControl } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { 
  YStack, 
  XStack, 
  Text, 
  Button, 
  ScrollView, 
  H2, 
  Card, 
  Paragraph, 
  Input,
  Sheet,
  Theme,
  View,
} from 'tamagui';
import { Plus, FolderPlus, FolderOpen, X, Search } from 'lucide-react-native';
import { useContentStore } from '@/state/contentStore';

export default function FoldersScreen() {
  const insets = useSafeAreaInsets();
  const router = useRouter();
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [position, setPosition] = useState(0);
  const [newFolderName, setNewFolderName] = useState('');
  
  const { folders, fetchFolders, createFolder, isLoading } = useContentStore();
  
  useEffect(() => {
    fetchFolders();
  }, [fetchFolders]);
  
  const handleCreateFolder = async () => {
    if (!newFolderName.trim()) return;
    
    const newFolder = await createFolder(newFolderName);
    if (newFolder) {
      setNewFolderName('');
      setPosition(0);
    }
  };
  
  const onRefresh = async () => {
    setRefreshing(true);
    await fetchFolders();
    setRefreshing(false);
  };
  
  // Filter folders based on search query
  const filteredFolders = folders.filter(folder => 
    folder.name.toLowerCase().includes(searchQuery.toLowerCase())
  );
  
  return (
    <Theme name="light">
      <YStack f={1} bg="$background">
        <ScrollView
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
        >
          <YStack pt={Platform.OS === 'ios' ? insets.top : insets.top + StatusBar.currentHeight} px="$4" pb="$8">
            {/* Header section */}
            <XStack justifyContent="space-between" alignItems="center" mb="$4">
              <H2>My Folders</H2>
              <Button 
                size="$3"
                circular
                bg="$blue10"
                icon={<FolderPlus color="white\" size={18} />}
                onPress={() => setPosition(1)}
              />
            </XStack>
            
            {/* Search */}
            <Input
              placeholder="Search folders..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              mb="$4"
              size="$4"
              borderWidth={1}
              borderColor="$borderColor"
              borderRadius="$4"
              icon={Search}
            />
            
            {/* Folder list */}
            <YStack space="$2">
              {filteredFolders.length > 0 ? (
                filteredFolders.map((folder) => (
                  <Card
                    key={folder.id}
                    bordered
                    elevate
                    size="$4"
                    mb="$2"
                    onPress={() => router.push(`/folders/${folder.id}`)}
                    animation="bouncy"
                    scale={0.97}
                    pressStyle={{ scale: 1 }}
                  >
                    <Card.Header padded>
                      <XStack alignItems="center" space="$2">
                        <FolderOpen size={20} color="#3694FF" />
                        <Text fontSize="$5" fontWeight="bold">{folder.name}</Text>
                      </XStack>
                    </Card.Header>
                    <Card.Footer padded>
                      <XStack justifyContent="space-between" alignItems="center" f={1}>
                        <Text theme="alt2" fontSize="$3">
                          Created: {new Date(folder.created_at).toLocaleDateString()}
                        </Text>
                        <Button 
                          size="$2" 
                          themeInverse 
                          onPress={() => router.push(`/folders/${folder.id}`)}
                        >
                          Open
                        </Button>
                      </XStack>
                    </Card.Footer>
                  </Card>
                ))
              ) : (
                <YStack space="$4" alignItems="center" py="$10">
                  <Text theme="alt2">No folders found</Text>
                  <Button 
                    onPress={() => setPosition(1)} 
                    size="$3" 
                    themeInverse
                    icon={<Plus size={16} />}
                  >
                    Create Folder
                  </Button>
                </YStack>
              )}
            </YStack>
          </YStack>
        </ScrollView>
        
        {/* New Folder Sheet */}
        <Sheet
          modal
          open={position > 0}
          onOpenChange={(open) => {
            if (!open) setPosition(0);
          }}
          snapPoints={[50]}
          position={position}
          onPositionChange={setPosition}
          dismissOnSnapToBottom
        >
          <Sheet.Overlay />
          <Sheet.Frame padding="$4">
            <Sheet.Handle />
            <YStack space="$4">
              <XStack justifyContent="space-between" alignItems="center">
                <H2>New Folder</H2>
                <Button
                  circular
                  icon={<X size={16} />}
                  onPress={() => setPosition(0)}
                  size="$3"
                  bg="$gray5"
                />
              </XStack>
              
              <Input
                placeholder="Folder Name"
                value={newFolderName}
                onChangeText={setNewFolderName}
                size="$4"
              />
              
              <Button
                themeInverse
                size="$4"
                onPress={handleCreateFolder}
                disabled={!newFolderName.trim() || isLoading}
                icon={<FolderPlus size={16} />}
              >
                {isLoading ? 'Creating...' : 'Create Folder'}
              </Button>
            </YStack>
          </Sheet.Frame>
        </Sheet>
      </YStack>
    </Theme>
  );
}