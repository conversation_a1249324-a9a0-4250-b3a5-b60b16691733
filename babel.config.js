module.exports = function (api) {
  api.cache(true);
  return {
    presets: [
      [
        'babel-preset-expo',
        {
          jsxImportSource: 'react',
          unstable_transformImportMeta: true
        }
      ],
      '@babel/preset-typescript'
    ],
    plugins: [
      // Temporarily disabled tamagui babel plugin for debugging
      // [
      //   '@tamagui/babel-plugin',
      //   {
      //     components: ['tamagui'],
      //     config: './tamagui.config.ts',
      //   }
      // ],
      '@babel/plugin-transform-class-static-block',
      'react-native-reanimated/plugin',
    ],
  };
};