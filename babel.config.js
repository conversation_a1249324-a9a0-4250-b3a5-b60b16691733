module.exports = function (api) {
  api.cache(true);
  return {
    presets: [
      ['babel-preset-expo', { jsxImportSource: 'react' }],
      '@babel/preset-typescript'
    ],
    plugins: [
      // Required for expo-router
      'expo-router/babel',
      // Temporarily disabled tamagui babel plugin for debugging
      // [
      //   '@tamagui/babel-plugin',
      //   {
      //     components: ['tamagui'],
      //     config: './tamagui.config.ts',
      //   }
      // ],
      'react-native-reanimated/plugin',
    ],
  };
};