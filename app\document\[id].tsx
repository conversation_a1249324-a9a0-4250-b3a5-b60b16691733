import React, { useEffect, useState } from 'react';
import { Platform, StatusBar } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { 
  YStack, 
  XStack, 
  Text, 
  Button, 
  ScrollView,
  H3,
  TextArea,
  Theme,
  View,
  Spinner,
} from 'tamagui';
import { ChevronLeft, BookText, Slash as FlashCard, FileQuestion, Share } from 'lucide-react-native';
import { useContentStore } from '@/state/contentStore';
import { extractTextFromPdf } from '@/lib/pdf';
import { extractTextFromImage } from '@/lib/ocr';
import { generateNotes } from '@/lib/ai';

export default function DocumentScreen() {
  const insets = useSafeAreaInsets();
  const router = useRouter();
  const { id } = useLocalSearchParams();
  const documentId = Array.isArray(id) ? id[0] : id;
  
  const [extractedText, setExtractedText] = useState<string>('');
  const [isExtracting, setIsExtracting] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedContent, setGeneratedContent] = useState<string>('');
  
  const { documents, fetchDocuments } = useContentStore();
  
  // Find the current document
  const currentDocument = documents.find(doc => doc.id === documentId);
  
  useEffect(() => {
    if (!currentDocument && documentId) {
      // If document not found, fetch it
      // This is a simplification as we'd need to fetch by ID in a real app
      const folderID = 'temp'; // This would be derived from your app state
      fetchDocuments(folderID);
    } else if (currentDocument) {
      extractDocumentText();
    }
  }, [currentDocument, documentId]);
  
  const extractDocumentText = async () => {
    if (!currentDocument) return;
    
    try {
      setIsExtracting(true);
      
      if (currentDocument.file_type === 'pdf') {
        const text = await extractTextFromPdf(currentDocument.file_url);
        setExtractedText(text);
      } else if (currentDocument.file_type === 'image') {
        const text = await extractTextFromImage(currentDocument.file_url);
        setExtractedText(text);
      }
    } catch (error) {
      console.error('Error extracting text:', error);
    } finally {
      setIsExtracting(false);
    }
  };
  
  const generateDocumentNotes = async () => {
    if (!extractedText) return;
    
    try {
      setIsGenerating(true);
      
      const notes = await generateNotes(extractedText);
      setGeneratedContent(notes);
    } catch (error) {
      console.error('Error generating notes:', error);
    } finally {
      setIsGenerating(false);
    }
  };
  
  return (
    <Theme name="light">
      <YStack f={1} bg="$background">
        <YStack pt={Platform.OS === 'ios' ? insets.top : insets.top + StatusBar.currentHeight} px="$4" pb="$8" f={1}>
          {/* Header section */}
          <XStack alignItems="center" space="$2" mb="$4">
            <Button
              size="$3"
              circular
              icon={<ChevronLeft size={18} />}
              onPress={() => router.back()}
              bg="$gray5"
            />
            <H3 numberOfLines={1} flex={1}>
              {currentDocument?.title || 'Document'}
            </H3>
            <Button
              size="$3"
              circular
              icon={<Share size={18} />}
              bg="$gray5"
            />
          </XStack>
          
          {/* Content area */}
          <ScrollView contentContainerStyle={{ flexGrow: 1 }}>
            {isExtracting ? (
              <YStack f={1} justifyContent="center" alignItems="center">
                <Spinner size="large" />
                <Text mt="$2">Extracting text...</Text>
              </YStack>
            ) : extractedText ? (
              <>
                <YStack space="$4">
                  <Text fontSize="$5" fontWeight="bold">Document Content</Text>
                  <TextArea
                    value={extractedText}
                    readOnly
                    multiline
                    numberOfLines={10}
                    size="$3"
                    height={200}
                  />
                  
                  {/* Actions */}
                  <XStack flexWrap="wrap" space="$2" justifyContent="center" mt="$2">
                    <Button 
                      size="$3"
                      icon={<BookText size={16} />}
                      bg="$blue10"
                      color="white"
                      onPress={generateDocumentNotes}
                      disabled={isGenerating}
                    >
                      Generate Notes
                    </Button>
                    <Button 
                      size="$3"
                      icon={<FlashCard size={16} />}
                      bg="$green10"
                      color="white"
                      disabled={true}
                    >
                      Create Flashcards
                    </Button>
                    <Button 
                      size="$3"
                      icon={<FileQuestion size={16} />}
                      bg="$purple10"
                      color="white"
                      disabled={true}
                    >
                      Create Quiz
                    </Button>
                  </XStack>
                </YStack>
                
                {isGenerating && (
                  <YStack space="$2" alignItems="center" mt="$6">
                    <Spinner size="large" />
                    <Text>Generating notes with AI...</Text>
                  </YStack>
                )}
                
                {generatedContent && !isGenerating && (
                  <YStack space="$4" mt="$6">
                    <Text fontSize="$5" fontWeight="bold">Generated Notes</Text>
                    <TextArea
                      value={generatedContent}
                      multiline
                      numberOfLines={15}
                      size="$3"
                    />
                    <Button themeInverse>
                      Save Notes
                    </Button>
                  </YStack>
                )}
              </>
            ) : (
              <YStack f={1} justifyContent="center" alignItems="center">
                <Text>No document content available</Text>
                <Button 
                  mt="$4" 
                  onPress={extractDocumentText}
                  themeInverse
                >
                  Retry Text Extraction
                </Button>
              </YStack>
            )}
          </ScrollView>
        </YStack>
      </YStack>
    </Theme>
  );
}