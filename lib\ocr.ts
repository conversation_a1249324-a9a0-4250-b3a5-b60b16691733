import { createWorker } from 'tesseract.js';

export async function extractTextFromImage(imageUri: string): Promise<string> {
  try {
    // Initialize the Tesseract worker
    const worker = await createWorker('eng');
    
    // Recognize text in the image
    const result = await worker.recognize(imageUri);
    
    // Terminate the worker
    await worker.terminate();
    
    return result.data.text;
  } catch (error) {
    console.error('Error extracting text from image:', error);
    throw new Error('Failed to extract text from image. Please try again later.');
  }
}